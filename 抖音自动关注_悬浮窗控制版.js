// 抖音自动关注脚本 - AutoJS6悬浮窗控制版
// 基于你提供的悬浮窗示例格式

"ui";

ui.layout(
  <vertical padding="16">
    <text text="抖音自动关注工具" textSize="18sp" gravity="center" margin="0 0 16 0"/>

    <text text="抖音号列表 (每行一个):" textSize="14sp"/>
    <input id="editUids" hint="请输入抖音号，每行一个" lines="8" gravity="top"/>

    <horizontal margin="16 0">
      <text text="关注间隔:" textSize="14sp"/>
      <input id="editInterval" text="5" inputType="number" w="60dp"/>
      <text text="秒" textSize="14sp"/>
    </horizontal>

    <horizontal margin="16 0">
      <text text="观看视频:" textSize="14sp"/>
      <input id="editWatchSec" text="10" inputType="number" w="60dp"/>
      <text text="秒" textSize="14sp"/>
    </horizontal>

    <horizontal margin="16 0">
      <checkbox id="chkSmart" text="智能模式" checked="true"/>
      <checkbox id="chkBrushVideo" text="刷视频" checked="false"/>
    </horizontal>

    <button id="btnStart" text="开始运行" style="Widget.AppCompat.Button.Colored"/>
    <button id="btnStop" text="停止运行" style="Widget.AppCompat.Button"/>

    <ScrollView>
      <text id="txtLog" textSize="12sp" maxLines="20"/>
    </ScrollView>
  </vertical>
);

// 启用自动执行
auto();

//设置运行框标题
console.setTitle("抖音自动关注");

//设置运行框初始的位置
console.setPosition(400, 100);

//判断运行框是否显示在屏幕上
var isconsoleshow = false;

// 全局变量
let running = false;
let paused = false;
let uidArr = [];
let curIndex = 0;

const PKG = "com.ss.android.ugc.aweme";

// 默认抖音号列表
const DEFAULT_UIDS = [
  "1106768009",
  "dyv9ib5txr5q",
  "68351324167",
  "clwy1314.",
  "dy7doa8okmrw",
  "1500941473",
  "71557529012"
];

// 初始化UI
ui.editUids.setText(DEFAULT_UIDS.join("\n"));

// 日志函数
function uiLog(msg) {
  console.log(new Date().toLocaleTimeString() + ": " + msg);
  ui.run(() => {
    let current = ui.txtLog.getText().toString();
    let newText = current + "\n" + new Date().toLocaleTimeString() + ": " + msg;
    let lines = newText.split("\n");
    if(lines.length > 20) {
      lines = lines.slice(-20);
      newText = lines.join("\n");
    }
    ui.txtLog.setText(newText);
  });
}

// 解析抖音号列表
function parseUids() {
  let text = ui.editUids.getText().toString().trim();
  if(!text) return DEFAULT_UIDS;
  return text.split(/[\r\n]+/).map(s => s.trim()).filter(s => s.length > 0);
}

// 获取配置
function getCfg() {
  return {
    interval: parseInt(ui.editInterval.getText()) || 5,
    watchSec: parseInt(ui.editWatchSec.getText()) || 10,
    smart: ui.chkSmart.isChecked(),
    brushVideo: ui.chkBrushVideo.isChecked()
  };
}

// 启动抖音
function launchDouyin() {
  uiLog("启动抖音...");
  app.launchPackage(PKG);
  sleep(3000);
  uiLog("抖音已启动");
}

// 打开搜索
function openSearch() {
  try {
    let searchBtn = textMatches(/搜索/).findOne(3000) || 
                   descMatches(/搜索/).findOne(1000) ||
                   idMatches(/search/).findOne(1000);
    if(searchBtn) {
      searchBtn.click();
      sleep(1000);
      return className("android.widget.EditText").findOne(2000);
    } else {
      // 兜底：点右上角区域
      click(device.width - 80, 120);
      sleep(1200);
      return className("android.widget.EditText").findOne(2000);
    }
  } catch(e) {
    uiLog("打开搜索失败: " + e);
  }
  return null;
}

// 执行搜索
function doSearch(uid) {
  uiLog("搜索: " + uid);
  let edit = openSearch();
  if(!edit) { 
    uiLog("未找到搜索输入框"); 
    return false; 
  }
  
  try {
    edit.setText("");
    sleep(300);
    edit.setText(uid);
    sleep(600);
    uiLog("输入完成, 执行搜索");
    
    // 执行搜索
    uiLog("准备执行搜索...");
    
    // 多种方式执行搜索
    let searchExecuted = false;

    // 方法1：按回车键
    try {
      if(press) {
        press("enter");
        searchExecuted = true;
        uiLog("通过回车键执行搜索");
      }
    } catch(e) {}

    // 方法2：查找搜索按钮（更精确的定位）
    if(!searchExecuted) {
      try {
        uiLog("准备查找搜索按钮");
        let searchBtn = null;

        // 尝试多种方式查找搜索按钮
        // 1. 查找包含"搜索"文本的TextView
        searchBtn = className("android.widget.TextView").textMatches(/搜索|确认|确定/).findOne(1000);
        if (!searchBtn) {
          // 2. 查找描述包含"搜索"的控件
          searchBtn = descMatches(/搜索|确认|确定/).findOne(1000);
        }
        if (!searchBtn) {
          // 3. 查找搜索框右侧的TextView（通常是搜索按钮）
          let editText = className("android.widget.EditText").findOne(1000);
          if (editText) {
            let editBounds = editText.bounds();
            // 在搜索框右侧查找TextView
            let textViews = className("android.widget.TextView").find();
            for (let i = 0; i < textViews.length; i++) {
              let tv = textViews[i];
              try {
                let tvBounds = tv.bounds();
                // 判断是否在搜索框右侧且高度相近
                if (tvBounds.left > editBounds.right &&
                    Math.abs(tvBounds.centerY() - editBounds.centerY()) < 50) {
                  searchBtn = tv;
                  break;
                }
              } catch(e) {}
            }
          }
        }

        if (searchBtn) {
          uiLog("找到搜索按钮，尝试点击");
          let clicked = false;

          // 尝试多种点击方式
          try {
            clicked = searchBtn.click();
            if (clicked) uiLog("直接点击成功");
          } catch (e1) {}

          if (!clicked) {
            try {
              clicked = searchBtn.performAction("CLICK");
              if (clicked) uiLog("performAction点击成功");
            } catch (e2) {}
          }

          if (!clicked) {
            try {
              let p = searchBtn.parent();
              if (p) {
                clicked = p.click();
                if (clicked) uiLog("点击父容器成功");
              }
            } catch (e3) {}
          }

          if (!clicked) {
            try {
              let bounds = searchBtn.bounds();
              if (bounds) {
                click(bounds.centerX(), bounds.centerY());
                clicked = true;
                uiLog("坐标点击成功");
              }
            } catch (e4) {}
          }

          if (clicked) {
            searchExecuted = true;
            uiLog("搜索按钮点击成功");
          } else {
            uiLog("搜索按钮点击失败");
          }
        } else {
          uiLog("未找到搜索按钮");
        }
      } catch(e) {
        uiLog("搜索按钮查找或点击异常: " + e);
      }
    }

    // 方法3：点击搜索框右侧区域（兜底方案）
    if(!searchExecuted) {
      try {
        uiLog("使用兜底方案：点击搜索框右侧");
        click(device.width * 0.9, device.height * 0.15);
        searchExecuted = true;
        uiLog("通过点击搜索框右侧执行搜索");
      } catch(e) {
        uiLog("兜底方案失败: " + e);
      }
    }

    if (!searchExecuted) {
      uiLog("所有搜索方法都失败了");
      return false;
    }
    
    // 等待搜索结果加载
    uiLog("等待搜索结果加载...");
    sleep(3000);
    
    // 检查是否成功进入搜索结果页面
    let searchResult = false;
    for(let i = 0; i < 10; i++) {
      if(text("用户").exists() || text("关注").exists() || text("已关注").exists()) {
        searchResult = true;
        uiLog("搜索结果页面加载成功");
        break;
      }
      sleep(500);
    }
    
    if(!searchResult) {
      uiLog("搜索结果页面加载失败");
      return false;
    }
    
    return true;
  } catch(e) { 
    uiLog("搜索失败:" + e); 
    return false; 
  }
}

// 进入第一个用户主页
function enterFirstUser(uid) {
  uiLog("进入第一个用户");

  try {
    // 先尝试点击 "用户" 标签
    let userTab = text("用户").findOne(2000);
    if(userTab){
      userTab.click();
      sleep(1500);
    }

    // 查找LynxFlattenUI控件并验证抖音号
    let userCards = className("com.lynx.tasm.behavior.ui.LynxFlattenUI").find();
    uiLog("找到 " + userCards.length + " 个LynxFlattenUI控件");

    for(let i = 0; i < userCards.length; i++) {
      try {
        let card = userCards[i];
        let desc = card.desc();
        uiLog("检查控件描述: " + desc);

        // 检查描述中是否包含目标抖音号
        if(desc && desc.includes(uid)) {
          uiLog("找到匹配的抖音号: " + uid + "，点击进入主页");
          card.click();
          sleep(2500);

          // 验证是否成功进入用户主页
          if(textMatches(/作品|粉丝|获赞/).findOne(2000)){
            uiLog("成功进入用户主页");
            return true;
          } else {
            uiLog("点击后未进入主页");
            return false;
          }
        } else {
          uiLog("抖音号不匹配，跳过此用户");
        }
      } catch(e) {
        uiLog("检查用户卡片异常: " + e);
      }
    }

    uiLog("未找到匹配抖音号的用户: " + uid);
    return false;

  } catch(e){
    uiLog("进入用户主页异常: " + e);
    return false;
  }
}

// 执行关注
function doFollow() {
  uiLog("尝试关注...");
  try {
    let followBtn = textMatches(/关注|\+ 关注/).findOne(2000);
    if(followBtn) {
      followBtn.click();
      sleep(1000);
      uiLog("关注成功");
      return true;
    } else {
      uiLog("已是关注状态");
      return true;
    }
  } catch(e) {
    uiLog("关注失败: " + e);
    return false;
  }
}

// 返回首页
function goHome() {
  uiLog("开始返回首页：准备按返回3次");
  for(let i = 1; i <= 3; i++) {
    back();
    uiLog("已按返回第 " + i + " 次");
    sleep(600);
  }
  uiLog("已按返回3次");
}

// 处理单个用户
function processUser(uid) {
  uiLog("处理: " + uid);
  
  // 1. 搜索用户
  if(!doSearch(uid)) {
    uiLog("搜索失败");
    return false;
  }
  
  // 2. 进入用户主页
  if(!enterFirstUser(uid)) {
    uiLog("未能进入用户主页");
    return false;
  }
  
  // 3. 执行关注
  if(!doFollow()) {
    uiLog("关注失败");
    return false;
  }
  
  // 4. 返回首页
  goHome();
  
  return true;
}

// 主运行函数
function startRun(){
  if(!auto.service){
    toast("请先开启无障碍服务");
    auto.waitFor();
  }

  uidArr = parseUids(); // 使用UI输入的列表
  let cfg = getCfg(); // 获取UI配置

  if(uidArr.length === 0){
    toast("请至少输入一个抖音号");
    return;
  }

  paused = false;
  curIndex = 0;

  uiLog(`开始，待处理 ${uidArr.length} 个`);
  uiLog("抖音号列表: " + uidArr.join(", "));
  uiLog("配置: 间隔" + cfg.interval + "秒, 观看" + cfg.watchSec + "秒");

  // 启动抖音
  launchDouyin();

  // 处理每个用户
  for(let i = 0; i < uidArr.length; i++) {
    if(!running) break;

    // 等待暂停结束
    while(paused && running) {
      sleep(1000);
    }

    if(!running) break;

    curIndex = i;
    let uid = uidArr[i];

    // 更新悬浮窗显示 - 使用ui.run确保在主线程执行
    if(window && window.startstopButton) {
      ui.run(() => {
        try {
          window.startstopButton.setText("停止");
        } catch(e) {
          // 忽略UI更新错误
        }
      });
    }

    if(processUser(uid)) {
      uiLog("处理成功: " + uid);
    } else {
      uiLog("处理失败: " + uid);
    }

    // 间隔等待
    if(i < uidArr.length - 1) {
      uiLog(`间隔 ${cfg.interval}s`);
      let waitTime = cfg.interval * 1000;
      let startWait = Date.now();
      while(Date.now() - startWait < waitTime && running && !paused) {
        sleep(100);
      }
    }
  }

  running = false;
  uiLog("全部处理完成");

  // 更新悬浮窗按钮 - 使用ui.run确保在主线程执行
  if(window && window.startstopButton) {
    ui.run(() => {
      try {
        window.startstopButton.setText("开始");
      } catch(e) {
        // 忽略UI更新错误
      }
    });
  }
  
  // 显示控制台
  if(!isconsoleshow) {
    threads.start(function() {
      console.show(true);
      isconsoleshow = true;
      try {
          console.setBackground("#44000000");
      } catch(e) {
          // 忽略setBackground错误
      }
    });
  }
}

// 定义开始按钮的响应函数
function startstopButtonClick() {
    toast("开始按钮被点击");

    if (window.startstopButton.getText() === "开始") {
        // 开始运行
        ui.run(() => {
            try {
                window.startstopButton.setText("停止");
            } catch(e) {
                // 忽略UI更新错误
            }
        });
        running = true;

        // 在新线程中运行主逻辑
        threads.start(function() {
            try {
                startRun();
            } catch(e) {
                uiLog("脚本运行出错: " + e);
                running = false;
                ui.run(() => {
                    try {
                        window.startstopButton.setText("开始");
                    } catch(e) {
                        // 忽略UI更新错误
                    }
                });
            }
        });
    }
    else {
        // 停止运行
        running = false;
        ui.run(() => {
            try {
                window.startstopButton.setText("开始");
            } catch(e) {
                // 忽略UI更新错误
            }
        });
        uiLog("用户停止运行");
    }
}

// 定义控制台按钮的响应函数
function consoleButtonClick() {
    toast("控制台按钮被点击");

    //这里对控制台状态操作需要启用新线程，否则当场卡死
    threads.start(function () {
        //如果当前是显示状态则隐藏
        if (isconsoleshow){
          console.hide() 
          //修改标记
          isconsoleshow=false 
        }
        else{
        //同理
        //填true是为了程序关闭时将控制台一同关闭
         console.show(true) 
         isconsoleshow=true  
        }
        try {
            console.setBackground("#44000000");
        } catch(e) {
            // 忽略setBackground错误
        }
    });
}

// 定义暂停按钮的响应函数
function pauseButtonClick() {
    toast("暂停按钮被点击");

    if (window.pauseButton.getText() === "暂停") {
        // 暂停运行
        paused = true;
        window.pauseButton.setText("继续");
        uiLog("已暂停");
    }
    else {
        // 继续运行
        paused = false;
        window.pauseButton.setText("暂停");
        uiLog("已继续");
    }
}



// 定义退出按钮的响应函数
function exitButtonClick() {
    toast("停止脚本并返回UI界面");
    // 停止脚本运行
    running = false;
    paused = false;
    
    // 关闭悬浮窗
    try {
        if(window) {
            window.close();
            window = null;
        }
    } catch(e) {
        // 忽略关闭悬浮窗的错误
    }
    
    // 隐藏控制台
    try {
        if(isconsoleshow) {
            console.hide();
            isconsoleshow = false;
        }
    } catch(e) {
        // 忽略控制台隐藏错误
    }
    
    // 返回UI界面
    uiLog("已停止脚本，返回UI界面");
}

// 悬浮窗变量声明
var window = null;

// UI按钮事件处理
ui.btnStart.click(() => {
  try {
    // 检查是否已有悬浮窗
    if(window) {
      toast('悬浮窗已存在，请先关闭');
      return;
    }
    
    // 创建悬浮窗
    buildFloat();
    // 不自动开始运行，等待用户点击悬浮窗的开始按钮
  } catch(e) {
    toast('创建悬浮窗失败: ' + e);
    // 如果悬浮窗失败，直接运行
    threads.start(function() {
      startRun();
    });
  }
});

ui.btnStop.click(() => {
  running = false;
  try{ if(window){ window.close(); window = null; } }catch(e){}
  uiLog("已停止运行");
});

// 应用退出时清理
events.on("exit", () => {
  running = false;
  try{ if(window){ window.close(); window = null; } }catch(e){}
});

// 创建悬浮窗函数
function buildFloat(){
  try {
    // 创建简洁的悬浮窗
    window = floaty.window(
      <vertical>
        <button id="startstopButton" text="开始" w="auto" h="auto"/>
        <button id="consoleButton" text="控制台" w="auto" h="auto"/>
                  <button id="exitButton" text="退出" w="auto" h="auto"/>
      </vertical>
    );

    window.exitOnClose();
    
    // 设置悬浮窗初始位置（屏幕中间）
    window.setPosition(device.width / 2 - 50, device.height / 2 - 50);
    
    uiLog("悬浮窗创建成功");
    
    // 创建悬浮窗时自动显示控制台
    threads.start(function() {
        sleep(500);
        console.show(true);
        isconsoleshow = true;
        try {
            console.setBackground("#44000000");
        } catch(e) {
            // 忽略setBackground错误
        }
    });

    // 设置开始停止按钮的点击事件监听器
    window.startstopButton.click(() => {
        startstopButtonClick();
    });

    // 设置控制台按钮的点击事件监听器
    window.consoleButton.click(() => {
        consoleButtonClick();
    });

    // 设置退出按钮的点击事件监听器
    window.exitButton.click(() => {
        exitButtonClick();
    });

    // 长按调整悬浮窗位置
    window.startstopButton.longClick(() => {
       window.setAdjustEnabled(!window.isAdjustEnabled());
       toast(window.isAdjustEnabled() ? "可拖动调整位置" : "已锁定位置");
       return true;
    });

  } catch(e) {
    uiLog("悬浮窗创建失败: " + e);
    window = null;
  }
}

// 保持程序运行状态
setInterval(() => {}, 1000);
