"ui";

/*
   爱之光内部脚本 (GUI)
  - 通过输入抖音 UID/抖音号，自动搜索并关注
  - 可批量处理，带间隔和随机等待，支持关注后看视频
  - 仅供学习与测试，请遵守平台规则与法律法规
*/

const PKG = "com.ss.android.ugc.aweme"; // 抖音包名
// 脚本宿主包名（AutoX本体），用于一键回到脚本首页
const HOST_PKG = (function(){ try{ return context.getPackageName(); }catch(e){ return null } })();

ui.layout(
  <vertical padding="16">
    <appbar>
      <toolbar title="爱之光内部" />
    </appbar>

    <ScrollView>
      <vertical>
        <card w="*" margin="8" cardCornerRadius="8dp">
          <vertical padding="12">
            <text text="UID/抖音号 (一行一个)" textStyle="bold" textSize="15sp"/>
            <input id="uids" text="1106768009" hint="例如: 123456789\nuser_abc" lines="6"/>
            <horizontal>
              <button id="btnLoad" text="从文件导入" style="Widget.AppCompat.Button.Borderless"/>
              <button id="btnClearUids" text="清空" style="Widget.AppCompat.Button.Borderless"/>
            </horizontal>
          </vertical>
        </card>

        <card w="*" margin="8" cardCornerRadius="8dp">
          <vertical padding="12">
            <text text="运行配置" textStyle="bold" textSize="15sp"/>
            <horizontal>
              <text text="关注间隔(秒)" w="110"/>
              <input id="intervalSec" text="5" inputType="number" w="80"/>
              <text text="  随机范围(秒)" w="110" gravity="right"/>
              <input id="randMin" text="2" inputType="number" w="60"/>
              <text text="-"/>
              <input id="randMax" text="6" inputType="number" w="60"/>
            </horizontal>
            <horizontal margin="8 0 0 0">
              <text text="看视频(秒)" w="110"/>
              <input id="watchSec" text="10" inputType="number" w="80"/>
              <checkbox id="cbAutoWatch" text="关注后观看视频" checked="true"/>
            </horizontal>
            <horizontal margin="8 0 0 0">
              <text text="刷视频个数" w="110"/>
              <input id="brushVideoCount" text="3" inputType="number" w="80"/>
              <checkbox id="cbBrushVideo" text="关注后刷视频" checked="true"/>
            </horizontal>
            <horizontal margin="8 0 0 0">
              <text text="刷视频时长" w="110"/>
              <input id="brushVideoTimeMin" text="15" inputType="number" w="60"/>
              <text text="-"/>
              <input id="brushVideoTimeMax" text="25" inputType="number" w="60"/>
              <text text="秒"/>
            </horizontal>
            <checkbox id="cbSmart" text="智能模式(随机等待/随机滑动)" checked="true"/>
          </vertical>
        </card>

        <card w="*" margin="8" cardCornerRadius="8dp">
          <vertical padding="12">
            <horizontal>
              <button id="btnStart" text="开始" style="Widget.AppCompat.Button.Colored" w="0" layout_weight="1"/>
              <button id="btnStop" text="停止" enabled="false" w="0" layout_weight="1"/>
            </horizontal>
            <text id="status" text="就绪" textColor="#666666" margin="8 8 0 0"/>
            <horizontal>
              <text text="进度:"/>
              <text id="progress" text="0/0" textStyle="bold" textColor="#1976D2"/>
            </horizontal>
            <progressbar id="pb"/>
          </vertical>
        </card>

        <card w="*" h="200dp" margin="8" cardCornerRadius="8dp">
          <vertical padding="12">
            <horizontal>
              <text text="日志" textStyle="bold" textSize="15sp" w="0" layout_weight="1"/>
              <button id="btnClearLog" text="清空日志" style="Widget.AppCompat.Button.Borderless"/>
            </horizontal>
            <ScrollView>
              <text id="log" textSize="12sp"/>
            </ScrollView>
          </vertical>
        </card>
      </vertical>
    </ScrollView>
  </vertical>
);

// 运行状态
let running = false;
let paused = false;
let uidArr = [];
let curIndex = 0;
let worker = null;

// 悬浮窗与日志
let fw = null;
let fwLogs = [];
function fLog(s){
  try{
    if (fw && fw.logList) {
      fwLogs.push(s);
      if (fwLogs.length > 80) fwLogs.shift();
      try{ fw.logList.setDataSource(fwLogs.slice()); }catch(e){}
    }
  }catch(e){}
}
// 等待悬浮窗子视图就绪
function waitFW(names, timeout){
  try{
    if(!fw) return false;
    timeout = timeout || 2000;
    const end = Date.now() + timeout;
    const list = Array.isArray(names) ? names : [names];
    while(Date.now() < end){
      let ok = true;
      for (let n of list){ if(!fw[n]){ ok=false; break; } }
      if(ok) return true;
      sleep(50);
    }
  }catch(e){}
  return false;
}

// 工具函数
function uiLog(s){
  let ts = new Date().toLocaleTimeString();
  const line = `[${ts}] ${s}`
  ui.run(()=> ui.log.setText(ui.log.getText() + line + "\n"));
  fLog(line)
  console.log(s);
}
function setStatus(s){ ui.run(()=> ui.status.setText(s)); }
function setProgress(i, total){
  ui.run(()=>{
    ui.progress.setText(`${i}/${total}`);
    try{ ui.pb.setMax(total); }catch(e){}
    try{ ui.pb.setProgress(i); }catch(e){}
  });
}

// 兼容获取文本（兼容 text() / getText() / text 属性）
function uiText(view){
  try{ if(typeof view.text === 'function') return String(view.text()); }catch(e){}
  try{ if(typeof view.getText === 'function') return String(view.getText()); }catch(e){}
  try{ if(typeof view.text === 'string') return String(view.text); }catch(e){}
  return '';
}

// 兼容性: 等待进入指定包名
function safeWaitForPackage(pkg, timeout){
  timeout = timeout || 10000;
  let t0 = Date.now();
  while(Date.now() - t0 < timeout){
    try{ if (currentPackage && currentPackage() === pkg) return true; }catch(e){}
    sleep(200);
  }
  return false;
}

// 兼容性: 回车键
function pressEnter(){
  try{ if (press){ press("enter"); return true; } }catch(e){}
  try{ if (typeof keyCode === 'function'){ keyCode(66); return true; } }catch(e){}
  try{ if (typeof KeyCode === 'function'){ KeyCode(66); return true; } }catch(e){}
  return false;
}

function getCfg(){
  return {
    intervalSec: Math.max(0, parseInt(ui.intervalSec.text()) || 5),
    randMin: Math.max(0, parseInt(ui.randMin.text()) || 2),
    randMax: Math.max(0, parseInt(ui.randMax.text()) || 6),
    watchSec: Math.max(0, parseInt(ui.watchSec.text()) || 10),
    autoWatch: !!ui.cbAutoWatch.checked,
    brushVideoCount: Math.max(0, parseInt(ui.brushVideoCount.text()) || 3),
    brushVideoTimeMin: Math.max(5, parseInt(ui.brushVideoTimeMin.text()) || 15),
    brushVideoTimeMax: Math.max(5, parseInt(ui.brushVideoTimeMax.text()) || 25),
    brushVideo: !!ui.cbBrushVideo.checked,
    smart: !!ui.cbSmart.checked,
  };
}
function parseUids(){
  return String(ui.uids.text() || "")
    .split(/\n+/)
    .map(s=>s.trim())
    .filter(Boolean);
}
function smartSleep(cfg){
  if(!cfg.smart) return;
  let ms = random(cfg.randMin*1000, cfg.randMax*1000);
  uiLog(`智能等待 ${(ms/1000).toFixed(1)} 秒`);
  sleep(ms);
}

// 启动抖音
function launchDouyin(){
  try{
    uiLog("启动抖音...");
    app.launch(PKG);
    let ok = safeWaitForPackage(PKG, 10000);
    if(!ok){ uiLog("未能进入抖音"); return false; }
    uiLog("抖音已启动");
    return true;
  }catch(e){ uiLog("启动异常: "+e); return false; }
}

// 通用: 点击若干选择器之一
function tryClick(selectors, timeout){
  timeout = timeout || 3000;
  for(let s of selectors){
    try{
      let n = s.findOne(timeout);
      if(n){ n.click(); sleep(500); return true; }
    }catch(e){}
  }
  return false;
}

// 进入搜索
function openSearch(){
  uiLog("尝试打开搜索");
  // 常见位置: 首页右上角的搜索图标 或 底部“搜索/发现”
  let ok = tryClick([
    descMatches(/搜索|Search/i),
    textMatches(/搜索|发现/),
    idMatches(/search|find|amj|a[l-z]{2}/) // 兼容不同版本 id
  ], 2500);
  if(!ok){
    // 兜底：点右上角区域
    click(device.width - 80, 120);
    sleep(1200);
  }
  // 等待输入框出现
  let edit = null;
  let cands = [
    idMatches(/search.*edit|et_search|kw/),
    className("android.widget.EditText")
  ];
  for(let s of cands){ edit = s.findOne(3000); if(edit) break; }
  return edit || null;
}

// 搜索用户
function searchUser(uid){
  setStatus("搜索:"+uid);
  let edit = openSearch();
  if(!edit){ uiLog("未找到搜索输入框"); return false; }
  try{
    edit.setText(""); sleep(300);
    edit.setText(uid); sleep(600);
    uiLog("输入完成, 执行搜索");
    pressEnter();
    sleep(3000);
  }catch(e){ uiLog("输入/搜索失败:"+e); return false; }

  // 切到“用户”标签
  tryClick([
    text("用户"), textContains("用户"),
    desc("用户"), descContains("用户")
  ], 2500);
  sleep(1200);
  return true;
}

// 进入首个搜索结果的用户主页（优先按抖音号控件，其次头像）
function enterFirstUser(uid){
  uiLog("进入第一个用户");

  function escapeRegex(s){ return String(s).replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); }
  function tryClickAncestor(n){
    let depth = 0;
    while(n && depth < 6){
      try{ if(n.clickable()){ n.click(); return true; } }catch(e){}
      try{ n = n.parent(); }catch(e){ n = null; }
      depth++;
    }
    return false;
  }
  function tryClickSiblingImage(n){
    let p = n;
    for(let d=0; d<4 && p; d++){
      try{
        // 在同一条目内找头像 ImageView
        let img = className("android.widget.ImageView").findOne(p, 50);
        if(img){ img.click(); return true; }
      }catch(e){}
      try{ p = p.parent(); }catch(e){ p = null; }
    }
    return false;
  }

  // 1) 先找抖音号 TextView
  let tv = null;
  try{
    tv = className("android.widget.TextView").text(uid).findOne(1500)
      || className("android.widget.TextView").textContains(uid).findOne(1500)
      || textMatches(new RegExp("(抖音号|抖音ID|ID)\\s*[:：]?\\s*" + escapeRegex(uid))).findOne(1500);
  }catch(e){}

  if(tv){
    uiLog("按抖音号命中，点击父容器进入");
    if(!tryClickAncestor(tv)){
      // 父容器不可点，尝试点击同条目的头像
      if(!tryClickSiblingImage(tv)){
        // 最后兜底直接点击文本区域
        try{ tv.click(); }catch(e){}
      }
    }
    sleep(2200);
  } else {
    uiLog("未命中抖音号，尝试点击头像");
    try{
      // 取最靠上的一个头像
      let imgs = className("android.widget.ImageView").find();
      let topImg = null, topY = 1e9;
      imgs.forEach(img=>{ try{ let b=img.bounds(); if(b && b.top>100 && b.top<topY){ topY=b.top; topImg=img; } }catch(e){} });
      if(topImg){ topImg.click(); sleep(2200); }
    }catch(e){}
  }

  // 检查是否进入用户主页
  let ok = textMatches(/作品|粉丝|获赞/).findOne(1800);
  if(!ok){
    // 再尝试点击一次稍下方区域
    click(parseInt(device.width/2), parseInt(device.height*0.38));
    sleep(1800);
    ok = textMatches(/作品|粉丝|获赞/).findOne(1200);
  }
  return !!ok;
}


// 安全点击：尝试可点击父级 -> node.click() -> 坐标点击
function safeClickNode(n, tag){
  try{
    if(!n){ return false; }
    let b = null;
    try{ b = n.bounds(); }catch(e){}

    // 1) 点击可点击父级
    try{
      let p = n, depth = 0;
      while(p && depth < 6){
        try{ if(p.clickable && p.clickable()){ uiLog(`${tag}: 点击可点击父级`); p.click(); sleep(300); return true; } }catch(e){}
        try{ p = p.parent(); }catch(e){ p = null; }
        depth++;
      }
    }catch(e){}

    // 2) 直接 node.click()
    try{ let r = n.click(); uiLog(`${tag}: node.click() -> ${r}`); sleep(300); if(r) return true; }catch(e){}

    // 3) 坐标点击
    try{
      if(b){
        let x = parseInt(b.centerX());
        let y = parseInt(b.centerY());
        uiLog(`${tag}: 坐标点击(${x},${y})`);
        click(x, y);
        sleep(300);
        return true;
      }
    }catch(e){}
  }catch(e){ uiLog(`${tag}: 点击异常 ${e}`); }
  return false;
}

// 执行关注
function doFollow(){
  uiLog("尝试关注...");
  sleep(800);
  // 已关注状态：已关注/互相关注/已取消
  if (textMatches(/已关注|互相关注|取消关注|已取消/).exists()){
    uiLog("已是关注状态");
    return true;
  }

  let success = false;

  // 方法1：通过 id "jbs" 直接找关注按钮
  try{
    let followBtn = id("jbs").findOne(2000);
    if(followBtn){
      uiLog("通过 id 找到关注按钮");
      if(!safeClickNode(followBtn, '关注按钮(id:jbs)')){
        uiLog('id按钮第一次点击失败，重试一次');
        safeClickNode(followBtn, '关注按钮(id:jbs)-重试');
      }
      sleep(1500);
      success = true;
    }
  }catch(e){
    uiLog("通过 id 查找失败: " + e);
  }

  // 方法2：通过文字直接找“关注”按钮
  if(!success){
    try{
      let tvFollow = textMatches(/^\s*\+?\s*关注(他|她|TA)?\s*$/).findOne(1500)
                  || text("关注").findOne(1000)
                  || descMatches(/^\s*\+?\s*关注(他|她|TA)?\s*$/).findOne(1000);
      if(tvFollow){
        try{ let b = tvFollow.bounds(); if(b){ uiLog(`关注文字 bounds: (${b.left},${b.top},${b.right},${b.bottom})`); } }catch(e){}
        uiLog("通过文字/描述找到关注控件");
        if(!safeClickNode(tvFollow, '关注文字/描述控件')){
          uiLog('文字控件第一次点击失败，重试一次');
          safeClickNode(tvFollow, '关注文字/描述控件-重试');
        }
        sleep(1500);
        success = true;
      } else {
        uiLog("未通过文字/描述命中关注控件");
      }
    }catch(e){ uiLog('文字方式查找异常: '+e); }
  }


  // 方法2：如果 id 方法失败，找不可点击的 FrameLayout + 包含"关注"文字
  if(!success){
    try{
      let list = className("android.widget.FrameLayout").find();
      let followBtn = null;

      list.forEach(n=>{
        try{
          let b = n.bounds(); if(!b) return;

          // 位置筛选：在屏幕下半部分，宽度较大（根据你的 bounds 信息）
          if(b.top < device.height*0.3 || b.top > device.height*0.9) return;
          if(b.width() < device.width*0.7 || b.height() < 100) return;

          // 检查是否包含"关注"文字（不管是否可点击）
          let hasFollow = false;
          try{
            hasFollow = textMatches(/^\s*\+?\s*关注\s*$/).findOne(n, 100) != null;
          }catch(e){}

          if(hasFollow){
            followBtn = n;
            uiLog("通过位置和文字找到关注按钮");
          }
        }catch(e){}
      });

      if(followBtn){
        if(!safeClickNode(followBtn, '关注容器(FrameLayout)')){
          uiLog('容器第一次点击失败，重试一次');
          safeClickNode(followBtn, '关注容器(FrameLayout)-重试');
        }
        sleep(1500);
        success = true;
      }
    }catch(e){
      uiLog("通过位置查找失败: " + e);
    }
  }

  // 方法3：兜底方案 - 根据你提供的坐标直接点击
  if(!success){
    uiLog("前两种方法都失败，使用坐标兜底");
    // 根据你提供的 bounds (44,1242,916,1374)，点击中心位置
    let centerX = (44 + 916) / 2;  // 480
    let centerY = (1242 + 1374) / 2;  // 1308
    // 使用坐标点击
    click(centerX, centerY);
    uiLog(`坐标点击(${centerX},${centerY})`);
    sleep(1500);
    success = true;
    uiLog("已点击关注区域坐标");
  }

  // 检查是否关注成功
  if (textMatches(/已关注|互相关注|取消关注|已取消/).findOne(1200)){
    uiLog("关注成功");
    return true;
  }

  uiLog("关注可能失败，未检测到已关注状态");
  return false;
}

// 看一个作品视频（可选）
function watchOne(cfg){
  if(!cfg.autoWatch) {
    uiLog("跳过观看视频（未启用）");
    return true;
  }

  uiLog(`观看视频 ${cfg.watchSec}s`);

  // 尝试点击第一个作品网格区域进入视频
  // 方法1：点击作品网格区域
  click(device.width/3, Math.max(600, device.height*0.55));
  sleep(1500);

  // 检查是否进入了视频播放界面
  if(currentPackage() === PKG){
    // 如果还在抖音主页，尝试点击屏幕中心
    click(device.width/2, device.height/2);
    sleep(1500);
  }

  // 再次检查是否成功进入视频
  if(currentPackage() === PKG){
    uiLog("成功进入视频播放界面");
  } else {
    uiLog("可能未成功进入视频，继续观看");
  }

  // 观看视频指定时间
  let watchTime = cfg.watchSec * 1000;
  uiLog(`开始观看视频 ${cfg.watchSec} 秒...`);

  // 在观看期间可以随机上下滑动刷视频
  let swipeCount = Math.floor(cfg.watchSec / 3); // 每3秒滑动一次
  for(let i = 0; i < swipeCount && i * 3000 < watchTime; i++){
    sleep(3000); // 等待3秒

    // 随机决定是否滑动（70%概率）
    if(random(0, 100) < 70){
      // 随机上滑或下滑
      if(random(0, 100) < 50){
        // 上滑到下一个视频
        swipe(device.width/2, device.height*0.8, device.width/2, device.height*0.2, random(300, 600));
        uiLog("上滑到下一个视频");
      } else {
        // 下滑到上一个视频
        swipe(device.width/2, device.height*0.2, device.width/2, device.height*0.8, random(300, 600));
        uiLog("下滑到上一个视频");
      }
      sleep(500);
    }
  }

  // 观看剩余时间
  let remainingTime = watchTime - (swipeCount * 3000);
  if(remainingTime > 0){
    sleep(remainingTime);
  }

  uiLog("视频观看完成，返回用户主页");

  // 返回到用户主页
  back();
  sleep(1000);

  return true;
}

// 返回首页（尽量）
function backToHome(){
  for(let i=0;i<3;i++){ back(); sleep(600); }
  // 可再尝试点“首页”
  tryClick([ text("首页"), desc("首页") ], 1200);
}

// 关注后刷视频（参考现有代码逻辑）
function brushVideosAfterFollow(cfg){
  if(!cfg.brushVideo) {
    uiLog("跳过刷视频（未启用）");
    return true;
  }

  uiLog(`开始刷视频，共 ${cfg.brushVideoCount} 个视频`);

  // 确保回到抖音主页
  backToHome();
  sleep(2000);

  // 点击推荐页面开始刷视频
  tryClick([ text("推荐"), desc("推荐") ], 1000);
  sleep(1000);

  // 刷指定数量的视频
  for(let i = 0; i < cfg.brushVideoCount; i++){
    if(!running) break;

    // 上滑到下一个视频（参考现有代码的滑动逻辑）
    swipe(Math.floor(device.width / 2), Math.floor(device.height / 8 * 6), Math.floor(device.width / 2), Math.floor(device.height / 8), 800);
    sleep(2500);

    // 随机观看时长
    let watchTime = random(cfg.brushVideoTimeMin, cfg.brushVideoTimeMax);
    uiLog(`正在观看第 ${i + 1}/${cfg.brushVideoCount} 个视频，观看 ${watchTime} 秒`);

    sleep(watchTime * 1000);

    // 随机等待1-3秒
    sleep(random(1000, 3000));
  }

  uiLog(`刷视频完成，共观看了 ${cfg.brushVideoCount} 个视频`);
  return true;
}

// 主流程: 关注一个 UID
function followOne(uid, cfg){
  try{
    if(!searchUser(uid)) return false;
    if(!enterFirstUser(uid)){ uiLog("未能进入用户主页"); return false; }

    if(!doFollow()){
      uiLog("关注失败或已关注");
    }

    if(cfg.smart){ // 随机轻滑
      if(random(0,100)<35){
        swipe(device.width*0.5, device.height*0.8, device.width*0.5, device.height*0.3, random(300,900));
        sleep(600);
      }
    }

    watchOne(cfg);

    // 关注完成后刷视频
    brushVideosAfterFollow(cfg);

    backToHome();
    return true;
  }catch(e){ uiLog("处理异常:"+e); backToHome(); return false; }
}

// 事件绑定
ui.btnClearUids.click(()=> ui.uids.setText(""));
ui.btnClearLog.click(()=> ui.log.setText(""));
ui.btnLoad.click(()=>{
  let p = dialogs.rawInput("输入文件路径","/sdcard/uid_list.txt");
  if(p && files.exists(p)){
    ui.uids.setText(files.read(p));
    uiLog("已载入: "+p);
  }else if(p){ toast("文件不存在"); }
});

// 悬浮窗 UI 构建
function buildFloat(){
  try{ if(fw){ try{fw.close();}catch(e){} fw=null } }catch(e){}
  // 参考“抖音关键词搜索 - 副本.js”的稳定写法
  fw = floaty.window(
    "<frame w='360dp' h='240dp' bg='#FFFFFF' alpha='0.95' cornerRadius='16dp' cardElevation='8dp'>" +
    "  <vertical padding='12' gravity='center'>" +
    "    <horizontal marginBottom='8'>" +
    "      <text text='任务：' textSize='12sp' textColor='#333333'/>" +
    "      <text id='taskProgress' text='0/0' textSize='12sp' textColor='#333333'/>" +
    "      <text text=' | ' textSize='12sp' textColor='#333333'/>" +
    "      <text text='状态：' textSize='12sp' textColor='#333333'/>" +
    "      <text id='runState' text='就绪' textSize='12sp' textColor='#333333'/>" +
    "    </horizontal>" +
    "    <text text='任务日志：' textSize='14sp' textColor='#333333' marginBottom='8' gravity='center'/>" +
    "    <list id='logList' h='120' w='*' bg='#F5F5F5' cornerRadius='8dp'>" +
    "      <text text='{{this}}' textSize='12sp' textColor='#666666' maxLines='1'/>" +
    "    </list>" +
    "    <horizontal w='*' marginTop='12'>" +
    "      <button id='pauseResume' text='开始' w='0' layout_weight='1' h='40' bg='#4CAF50' textColor='#FFFFFF' cornerRadius='8dp' textSize='14sp'/>" +
    "      <button id='back' text='脚本' w='0' layout_weight='1' h='40' bg='#FF9800' textColor='#FFFFFF' marginLeft='8' cornerRadius='8dp' textSize='14sp'/>" +
    "      <button id='stop' text='结束' w='0' layout_weight='1' h='40' bg='#F44336' textColor='#FFFFFF' marginLeft='8' cornerRadius='8dp' textSize='14sp'/>" +
    "    </horizontal>" +
    "  </vertical>" +
    "</frame>"
  );
  try{ fw.setPosition(60, 200); }catch(e){}
  try{ fw.setAdjustEnabled(true); }catch(e){}

  fwLogs = [];
  try{ fw.logList.setDataSource([]); }catch(e){}

  // 等待子控件就绪再绑定事件
  if(!waitFW(['pauseResume','back','stop','logList','runState','taskProgress'], 3000)){
    uiLog('悬浮窗子控件未就绪，创建失败');
    return;
  }

  // 开始/暂停
  try{ fw.pauseResume.setOnClickListener(function(){
    if(!running){ startRun(); fw.pauseResume.setText('暂停'); }
    else { paused = !paused; fw.pauseResume.setText(paused? '继续' : '暂停'); uiLog(paused? '已暂停' : '继续运行'); }
  }); }catch(e){}

  // 返回脚本首页
  try{ fw.back.setOnClickListener(function(){
    try{
      paused = true; uiLog('尝试返回脚本首页');
      app.startActivity({ packageName: context.getPackageName(), className: 'com.stardust.autojs.execution.ScriptExecuteActivity' });
    }catch(e){ uiLog('返回脚本失败:'+e) }
  }); }catch(e){}

  // 结束
  try{ fw.stop.setOnClickListener(function(){
    try{
      running=false; paused=false; uiLog('结束运行，返回脚本首页');
      app.startActivity({ packageName: context.getPackageName(), className: 'com.stardust.autojs.execution.ScriptExecuteActivity' });
      try{ fw.close(); }catch(e){}
    }catch(e){}
    fw=null;
  }); }catch(e){}
}

function startRun(){
  if(running) return;
  if(!auto.service){ toast("请先开启无障碍服务"); auto.waitFor(); }
  uidArr = parseUids();
  if(uidArr.length===0){ toast("请至少输入一个 UID/抖音号"); return; }
  let cfg = getCfg();

  running = true; paused = false; curIndex = 0;
  ui.btnStart.enabled = false;
  ui.btnStop.enabled = true;
  setProgress(0, uidArr.length);
  setStatus("准备启动抖音...");
  uiLog(`开始，待处理 ${uidArr.length} 个`);
  try{ if(fw && fw.pauseResume) fw.pauseResume.setText('暂停') }catch(e){}

  // 启动后台线程（兼容模式）
  function runLoop(){
    try{
      if(!launchDouyin()){ setStatus("启动失败"); running=false; ui.run(()=>{ ui.btnStart.enabled=true; ui.btnStop.enabled=false; }); return; }
      for(let i=0;i<uidArr.length && running;i++){
        while(paused && running){ sleep(400) }
        curIndex = i;
        setProgress(i, uidArr.length);
        let u = uidArr[i];
        setStatus(`处理(${i+1}/${uidArr.length}): ${u}`);
        uiLog(`处理: ${u}`);

        followOne(u, cfg);

        if(i < uidArr.length-1 && running){
          uiLog(`间隔 ${cfg.intervalSec}s`);
          for(let t=0; t<cfg.intervalSec && running && !paused; t++){ sleep(1000) }
          smartSleep(cfg);
        }
      }
      if(running){
        setProgress(uidArr.length, uidArr.length);
        setStatus("完成");
        uiLog("全部完成");
      }
    }catch(e){ uiLog('运行异常: '+e) }
    finally{
      running=false;
      ui.run(()=>{ ui.btnStart.enabled=true; ui.btnStop.enabled=false; });
      try{ if(fw && fw.pauseResume) fw.pauseResume.setText('开始') }catch(e){}
    }
  }

  try{
    if(worker && worker.isAlive()) try{ worker.interrupt(); }catch(e){}
    worker = threads.start(()=>{ runLoop(); });
  }catch(e){
    uiLog('线程启动失败，使用兼容方式: '+e);
    try{
      let Runnable = java.lang.Runnable;
      let Thread = java.lang.Thread;
      worker = new Thread(new Runnable({ run: function(){ runLoop(); } }));
      worker.start();
    }catch(e2){ uiLog('兼容线程也失败: '+e2); running=false; return; }
  }
}

ui.btnStart.click(()=>{
  // 打开悬浮窗并开始
  try {
    // AutoJS6中直接尝试创建悬浮窗，如果没有权限会自动请求
    buildFloat();
    startRun();
  } catch(e) {
    // 如果创建悬浮窗失败，提示用户授权
    toast('请授权悬浮窗权限');
    try {
      // 尝试请求悬浮窗权限
      if(floaty.requestPermission) {
        floaty.requestPermission();
      } else {
        // 如果没有requestPermission方法，引导用户手动开启
        toast('请手动开启悬浮窗权限后重试');
      }
    } catch(e2) {
      toast('请手动开启悬浮窗权限后重试');
    }
  }
});

ui.btnStop.click(()=>{
  if(!running) return;
  running = false;
  setStatus("已停止");
  uiLog("用户手动停止");
  ui.btnStart.enabled = true;
  ui.btnStop.enabled = false;
  try{ if(fw && fw.pauseResume) fw.pauseResume.setText('开始') }catch(e){}
});

// 初始提示
ui.post(()=>{
  uiLog("提示: 首次运行请开启无障碍与悬浮窗权限");
  uiLog("如果搜索/关注按钮找不到，请适当修改选择器或手动辅助一次");
});

// 防止某些环境下 UI 线程提前退出（保持心跳）
setInterval(()=>{}, 1000);

