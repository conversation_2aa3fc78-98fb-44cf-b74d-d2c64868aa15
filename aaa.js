// Auto.js 6 脚本：抖音自动加好友并发私信
// 抖音已登录账号

let douyinId = "1996619267"; // ?? 修改成你想添加的抖音号
let message = "你好，我看了你的作品觉得非常喜欢，可以交个朋友吗";

auto.waitFor(); // 等待无障碍服务
app.launchApp("抖音"); // 启动抖音
toast("正在打开抖音...");
sleep(8000); // 等待抖音首页加载

try {
  // 点击搜索图标（可能右上角或底部）
  if (textMatches(/搜索|放大镜/).exists()) {
    textMatches(/搜索|放大镜/).findOne().click();
  } else if (desc("搜索").exists()) {
    desc("搜索").findOne().click();
  }
  sleep(2000);

  // 输入抖音号并搜索
  setText(douyinId);
  sleep(1000);
  press("search"); // 模拟按下搜索
  sleep(4000);

  // 点击第一个搜索结果进入主页
  if (text("关注").exists() || text("已关注").exists()) {
    text("关注").findOne().parent().click(); // 点击用户卡片
  } else {
    toast("未找到账号，可能不存在");
    exit();
  }
  sleep(3000);

  // 点击“添加好友”或“关注”
  if (text("添加好友").exists()) {
    text("添加好友").findOne().click();
    sleep(2000);
  } else if (text("关注").exists()) {
    text("关注").findOne().click();
    sleep(2000);
  }

  // 点击“私信”
  if (text("私信").exists()) {
    text("私信").findOne().click();
    sleep(2000);

    // 输入消息内容
    let input = className("EditText").findOne();
    input.setText(message);
    sleep(500);

    // 点击发送按钮（假设是带“发送”文字的）
    if (text("发送").exists()) {
      text("发送").findOne().click();
      toast("消息已发送！");
    } else {
      toast("未找到发送按钮");
    }
  } else {
    toast("无法私信此用户");
  }
} catch (err) {
  toast("运行出错：" + err);
}