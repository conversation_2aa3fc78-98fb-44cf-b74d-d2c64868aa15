// 抖音自动关注脚本 - AutoJS6兼容版本

auto.waitFor();

// 全局变量
let running = false;
let paused = false;

// 日志函数
function uiLog(msg) {
  console.log(msg);
}

// 启动抖音
function launchDouyin() {
  uiLog("启动抖音...");
  app.launchPackage("com.ss.android.ugc.aweme");
  sleep(3000);
  uiLog("抖音已启动");
}

// 打开搜索
function openSearch() {
  try {
    let searchBtn = textMatches(/搜索/).findOne(3000) || 
                   descMatches(/搜索/).findOne(1000) ||
                   idMatches(/search/).findOne(1000);
    if(searchBtn) {
      searchBtn.click();
      sleep(1000);
      return className("android.widget.EditText").findOne(2000);
    }
  } catch(e) {
    uiLog("打开搜索失败: " + e);
  }
  return null;
}

// 执行搜索
function doSearch(uid) {
  uiLog("尝试打开搜索");
  let edit = openSearch();
  if(!edit) { 
    uiLog("未找到搜索输入框"); 
    return false; 
  }
  
  try {
    edit.setText("");
    sleep(300);
    edit.setText(uid);
    sleep(600);
    uiLog("输入完成, 执行搜索");
    
    // 多种方式执行搜索
    let searchExecuted = false;
    
    // 方法1：按回车键
    try {
      if(press) {
        press("enter");
        searchExecuted = true;
        uiLog("通过回车键执行搜索");
      }
    } catch(e) {}
    
    // 方法2：查找搜索按钮
    if(!searchExecuted) {
      let searchBtn = textMatches(/搜索|确认|确定/).findOne(1000);
      if(searchBtn) {
        searchBtn.click();
        searchExecuted = true;
        uiLog("通过搜索按钮执行搜索");
      }
    }
    
    sleep(3000);
    return true;
  } catch(e) { 
    uiLog("输入/搜索失败:" + e); 
    return false; 
  }
}

// 进入第一个用户主页 - AutoJS6兼容版本
function enterFirstUser(uid) {
  uiLog("进入第一个用户");
  
  // 等待页面稳定
  sleep(2000);
  
  // 输出当前页面信息
  try {
    let allTexts = className("android.widget.TextView").find();
    let pageTexts = [];
    for(let i = 0; i < Math.min(allTexts.length, 10); i++) {
      try {
        let text = allTexts[i].text();
        if(text && text.length > 0 && text.length < 20) {
          pageTexts.push(text);
        }
      } catch(e) {}
    }
    uiLog("当前页面主要文本: " + pageTexts.join(", "));
  } catch(e) {
    uiLog("获取页面信息失败: " + e);
  }
  
  // 点击第一个搜索结果进入主页
  if (text("关注").exists() || text("已关注").exists()) {
    uiLog("找到关注按钮，点击用户卡片进入主页");
    try {
      let followBtn = text("关注").findOne(1000) || text("已关注").findOne(1000);
      if(followBtn) {
        followBtn.parent().click(); // 点击用户卡片
        sleep(2000);
        
        // 检查是否成功进入用户主页
        if(textMatches(/作品|粉丝|获赞/).findOne(2000)) {
          uiLog("通过关注按钮成功进入用户主页");
          return true;
        } else {
          uiLog("点击用户卡片后未进入主页");
        }
      }
    } catch(e) {
      uiLog("通过关注按钮点击失败: " + e);
    }
  } else {
    uiLog("未找到关注按钮，可能账号不存在");
  }
  
  return false;
}

// 执行关注
function doFollow() {
  uiLog("尝试关注...");
  try {
    let followBtn = textMatches(/关注|\+ 关注/).findOne(2000);
    if(followBtn) {
      followBtn.click();
      sleep(1000);
      uiLog("关注成功");
      return true;
    } else {
      uiLog("已是关注状态");
      return true;
    }
  } catch(e) {
    uiLog("关注失败: " + e);
    return false;
  }
}

// 返回首页
function goHome() {
  uiLog("开始返回首页：准备按返回3次");
  for(let i = 1; i <= 3; i++) {
    back();
    uiLog("已按返回第 " + i + " 次");
    sleep(600);
  }
  uiLog("已按返回3次");
}

// 处理单个用户
function processUser(uid) {
  uiLog("处理: " + uid);
  
  // 1. 搜索用户
  if(!doSearch(uid)) {
    uiLog("搜索失败");
    return false;
  }
  
  // 2. 进入用户主页
  if(!enterFirstUser(uid)) {
    uiLog("未能进入用户主页");
    return false;
  }
  
  // 3. 执行关注
  if(!doFollow()) {
    uiLog("关注失败");
    return false;
  }
  
  // 4. 返回首页
  goHome();
  
  return true;
}

// 主函数
function main() {
  // 测试用的抖音号列表
  let uidList = [
    "1106768009",
    "dyv9ib5txr5q", 
    "68351324167"
  ];
  
  uiLog("开始，待处理 " + uidList.length + " 个");
  
  // 启动抖音
  launchDouyin();
  
  // 处理每个用户
  for(let i = 0; i < uidList.length; i++) {
    let uid = uidList[i];
    
    if(processUser(uid)) {
      uiLog("处理成功: " + uid);
    } else {
      uiLog("处理失败: " + uid);
    }
    
    // 间隔等待
    if(i < uidList.length - 1) {
      uiLog("间隔 5s");
      sleep(5000);
    }
  }
  
  uiLog("全部处理完成");
}

// 启动脚本
main();
