const 宽度 = device.getScreenWidth();
const 高度 = device.getScreenHeight();
let 延时最小值 = readConfigString("延时最小值")*1000;
let 延时最大值 = readConfigString("延时最大值")*1000;
let 添加次数 = readConfigString("添加次数");
let 休息分钟 = readConfigString("休息分钟");
let 休息分钟2 = readConfigString("休息分钟");
let 关键词 = readConfigString("关键词");
let 私信话术 = readConfigString("私信话术");
let 输入卡密 = readConfigString("输入卡密");
let 功能选择 = readConfigString("功能选择");
let 关键词检测 = readConfigBoolean("关键词检测");
let 评论图片 = readConfigBoolean("评论图片");
let 点赞率 = readConfigString("点赞率");
let 关注率 = readConfigString("关注率");
let 收藏率 = readConfigString("收藏率");
let 运行次数 = 0

let pjysdk = new PJYSDK("c96gu6rdqusvjiumoi0g", "nDoVKXJOjN0Qt75q104WPzbB3zk2uW7q");
pjysdk.debug = false;
pjysdk.SetCard(输入卡密);


// 心跳失败回调
pjysdk.onHeartbeatFailed(function(hret) {
    logw("心跳失败，2s后尝试重登...");
    sleep(2000);
    let login_ret = pjysdk.CardLogin();
    if (login_ret.code === 0) {
        logw("重登成功");
    } else {
        logw(login_ret.message);  // 重登失败
        sleep(200);
        exit();  // 退出脚本
    }
});
setStopCallback(function() {
    pjysdk.CardLogout(); // 调用退出登录
    logw("结束运行");
});
setExceptionCallback(function(msg) {
    pjysdk.CardLogout(); // 调用退出登录
    logw(" 异常停止消息: "+msg);
});
main();

//古圣团队开发  支持定制开发  招收学员！！！
//古圣团队开发  支持定制开发  招收学员！！！
//古圣团队开发  支持定制开发  招收学员！！！
//古圣团队开发  支持定制开发  招收学员！！！

function main() {
    let login_ret = pjysdk.CardLogin();
    if (login_ret.code !== 0) {
        // 登录失败提示
        toast(login_ret.message);
    }else {
        // 登录成功开始执行
        主程序()
    }

}

function 主程序() {
    if (!自动化服务检测(3)) {
        logw("自动化服务启动失败，无法执行脚本")
        exit();
        return;
    }
    logw("开始执行...")
    if (功能选择 === "智能养号") {
        for (let i = 0; i < 1000; i++) {
            智能养号()
        }
    }else if (功能选择 === "作品评论") {
        for (let i = 0; i < 1000; i++) {
            作品评论()
        }
    }else if (功能选择 === "评论区点赞") {
        for (let i = 0; i < 1000; i++) {
            评论区点赞()
        }
    }else if (功能选择 === "评论区私信") {

        if (关键词检测) {
            //用户选择关键词检测功能
            ui.toast("用户选择关键词检测发私信")
            logw("用户选择关键词检测发私信");
            for (let i = 0; i < 1000; i++) {
                评论区检测关键词私信()
            }
        }else {
            //用户没有选择关键词检测功能
            ui.toast("用户选择发送所有私信")
            logw("用户选择发送所有私信");
            for (let i = 0; i < 1000; i++) {
                评论区不检测关键词私信()
            }
        }
    }
}

function 智能养号() {
    let 发送延时 = random(延时最小值, 延时最大值);   //取随机延时
    let 最后的分钟数 = random(休息分钟, 休息分钟2)*60000;   //取随机延时
    //下面开始写代码
    if (运行次数 < 添加次数) {
        //这里写代码
        //下面是判断是否是直播间的代码
        let 是否是直播间 = getOneNodeInfo(text("点击进入直播间"),1000)
        if (是否是直播间) {
            logw("这是直播间，跳过！");
            ui.toast("这是直播间，跳过！")
            swipeToPoint(宽度/2,高度/10*8,宽度/2,高度/10*2,200)
            sleep(发送延时);
        }else {
            ui.toast("不是直播间，开始看视频")
            logw("不是直播间，开始看视频");
            sleep(发送延时);
        }
        //下面是观看视频时长判断
        let 观看时长 = random(10, 30);   //取随机观看时长
        for (let i = 0; i < 观看时长; i++) {
            let 倒计时 = 观看时长-i
            logd("倒计时"+倒计时+"秒")
            ui.toast("倒计时"+倒计时+"秒")
            sleep(1000)
        }


        sleep(观看时长);    //观看时长看视频
        //下面是判断是否用关注
        let 是否需要关注 = utils.getRatio(关注率);
        if (是否需要关注) {
            //需要关注
            let 关注子节点 = desc("关注").getOneNodeInfo(1000).child(0)
            if (关注子节点) {
                关注子节点.click()
                ui.toast("关注成功")
                logw("关注成功");
                sleep(发送延时);
            }else {
                //关注过了
                ui.toast("关注过的用户！")
                logw("关注过的用户！");
                sleep(发送延时);
            }
        }else {
            //不需要关注
            ui.toast("不需要关注！")
            logw("不需要关注");
            sleep(发送延时);
        }
        //下面是判断是否用点赞
        let 是否需要点赞 = utils.getRatio(点赞率);
        if (是否需要点赞) {
            //需要点赞
            click(descMatch("未点赞.*"))
            ui.toast("点赞成功！")
            logw("点赞成功");
            sleep(发送延时);
        }else {
            //不需要点赞
            ui.toast("不需要点赞！")
            logw("不需要点赞");
            sleep(发送延时);
        }
        //下面是判断是否用收藏
        let 是否需要收藏 = utils.getRatio(收藏率);
        if (是否需要收藏) {
            //需要收藏
            click(descMatch("未选中.*"))
            ui.toast("收藏成功！")
            logw("收藏成功");
            sleep(发送延时);
        }else {
            //不需要收藏
            ui.toast("不需要收藏！")
            logw("不需要收藏");
            sleep(发送延时);
        }
        sleep(发送延时);
        swipeToPoint(宽度/2,高度/10*8,宽度/2,高度/10*2,200)  //滑动
        sleep(发送延时);
        运行次数=运行次数+1
    }else {
        ui.toast("正在暂停中！")
        logd("正在暂停中");
        sleep(最后的分钟数);
        运行次数=0
    }
}

function 作品评论() {
    let 发送延时 = random(延时最小值, 延时最大值);   //取随机延时
    let 最后的分钟数 = random(休息分钟, 休息分钟2)*60000;   //取随机延时
    //下面开始写代码
    if (运行次数 < 添加次数) {
        //这里写代码
        运行次数=运行次数+1
        let 评论按钮 = getOneNodeInfo(descMatch("评论.*按钮"),1000)
        if (评论按钮) {
            ui.toast("找到评论按钮开始评论")
            logw("找到评论按钮开始评论");
            评论按钮.click()//点击评论按钮
            sleep(发送延时);
            发送评论程序()
            sleep(发送延时);
            back()  //返回到作品
            sleep(发送延时);
            ui.toast("评论完成")
            logw("评论完成");
            运行次数=运行次数+1
        }
        ui.toast("滑动到下一个作品")
        logw("滑动到下一个作品");
        swipeToPoint(宽度/2,高度/10*8,宽度/2,高度/10*2,200)  //滑动
        sleep(发送延时);
    }else {
        logd("正在暂停中");
        sleep(最后的分钟数);
        运行次数=0
    }
}

function 评论区点赞() {
    let 发送延时 = random(延时最小值, 延时最大值);   //取随机延时
    let 最后的分钟数 = random(休息分钟, 休息分钟2)*60000;   //取随机延时
    //下面开始写代码
    if (运行次数 < 添加次数) {
        //这里写代码
        let 点赞 = descMatch("赞.*未选中");
        if (点赞) {
            click(点赞)
            sleep(发送延时);
            运行次数 = 运行次数 + 1
            swipeToPoint(宽度 / 2, 高度 / 10 * 8, 宽度 / 2, 高度 / 10 * 7, 200)
            sleep(发送延时);
        }
    }else {
        logd("正在暂停中");
        sleep(最后的分钟数);
        运行次数=0
    }
}

function 评论区检测关键词私信() {
    let 发送延时 = random(延时最小值, 延时最大值);   //取随机延时
    let 最后的分钟数 = random(休息分钟, 休息分钟2)*60000;   //取随机延时
    let 关键词数组 = 关键词.split("丨");   //分割话术为数组
    //下面开始写代码
    for (let i = 0; i < 关键词数组.length; i++) {
        let 关键词 = getOneNodeInfo(textMatch(".*"+关键词数组[i]+".*"),1000)
        if (关键词) {
            logw("匹配到关键词了,关键词为："+关键词数组[i]);
            ui.toast("匹配到关键词了,关键词为："+关键词数组[i])
            clickPoint(关键词.bounds.left,关键词.bounds.top-30)    //点击找到的关键词内容正上方名字
            sleep(发送延时);
            let 更多 = getOneNodeInfo(desc("更多"),1000)    //点击右上角找到更多
            if (更多) {
                //找到更多了，发送私信
                sleep(发送延时);
                更多.click()   //点击更多
                sleep(发送延时);
                if (运行次数 < 添加次数) {
                    //这里写代码
                    发送私信流程()
                    运行次数 = 运行次数+1
                }else {
                    发送私信流程()
                    logw("开始休息，需要休息"+最后的分钟数*60000+"分钟");
                    ui.toast("开始休息，需要休息"+最后的分钟数*60000+"分钟")
                    sleep(最后的分钟数);
                    运行次数=0
                }
            }else {
                //没找到向下滑动评论区
                swipeToPoint(宽度/2,高度/10*8,宽度/2,高度/10*6,200)   //向下稍微滑动一点
                sleep(发送延时);
            }
        }

    }
    //所有关键词都检测完成，滑动下一页！
    logw("所有关键词都检测完成，滑动下一页！");
    ui.toast("所有关键词都检测完成，滑动下一页！")
    swipeToPoint(宽度/2,高度/10*8,宽度/2,高度/10*5,200)   //向下稍微滑动一点
    sleep(发送延时);
}

function 评论区不检测关键词私信() {
    let 发送延时 = random(延时最小值, 延时最大值);   //取随机延时
    let 最后的分钟数 = random(休息分钟, 休息分钟2)*60000;   //取随机延时
    //下面开始写代码
    if (运行次数 < 添加次数) {
        //这里写代码
        let 头像 = getNodeInfo(id("com.ss.android.ugc.aweme:id/avatar"),1000)
        for (let i = 0; i < 头像.length; i++) {
            头像[i].click() //点击头像
            sleep(发送延时);
            click(desc("更多"))
            sleep(发送延时);
            click(text("发私信"))
            sleep(发送延时);
            不检测关键词发送私信流程()
            sleep(发送延时);
            back() //返回
            sleep(发送延时);
            back() //返回
            sleep(发送延时);
            back() //返回
            sleep(发送延时);
            运行次数 = 运行次数 + 1
        }
        swipeToPoint(宽度 / 2, 高度 / 10 * 8, 宽度 / 2, 高度 / 10 , 1000)
        sleep(发送延时);
        swipeToPoint(宽度 / 2, 高度 / 10 * 8, 宽度 / 2, 高度 / 10*6 , 200)
        sleep(发送延时);
        let 暂时没有更多了 = getOneNodeInfo(text("暂时没有更多了"),1000)
        if (暂时没有更多了) {
            //找到暂时没有更多了，证明这个评论区发完了，换个作品。
            sleep(发送延时);
            back()  //返回
            sleep(发送延时);
            swipeToPoint(宽度 / 2, 高度 / 10 * 8, 宽度 / 2, 高度 / 10 * 2 , 800)  //滑动到下一个作品
            sleep(发送延时);
            let 评论按钮 = descMatch("评论.*，按钮")
            click(评论按钮)   //点击评论按钮
            sleep(发送延时);
        }
    }else {
        logd("正在暂停中");
        sleep(最后的分钟数);
        运行次数=0
    }
}






//下面函数不用修改!!!!!!!!!!!!!!!!!!!



function 发送私信流程() {
    logw("开始发送私信");
    ui.toast("开始发送私信")
    let 发送延时 = random(延时最小值, 延时最大值);   //取随机延时
    let 私信话术数组 = 私信话术.split("丨");   //分割话术为数组
    let 随机行数 = random(0, 私信话术数组.length-1);   //取随机行数
    //下面开始写代码.
    click(text("发私信"))    //点击发私信
    sleep(发送延时);
    click(clz("android.widget.EditText"))    //点击输入框
    sleep(发送延时);
    inputText(clz("android.widget.EditText"),私信话术数组[随机行数])
    sleep(发送延时);
    click(desc("发送"))
    sleep(发送延时);
    back()    //返回1次
    sleep(发送延时);
    back()    //返回2次
    sleep(发送延时);
    back()    //返回3次
    sleep(发送延时);
    swipeToPoint(宽度/2,高度/10*8,宽度/2,高度/10*5,200)   //向下稍微滑动一点
    sleep(发送延时);
    swipeToPoint(宽度/2,高度/10*8,宽度/2,高度/10*5,200)   //向下稍微滑动一点
    sleep(发送延时);
    swipeToPoint(宽度/2,高度/10*8,宽度/2,高度/10*5,200)   //向下稍微滑动一点
    sleep(发送延时);
    logw("发送私信完成");
    ui.toast("发送私信完成")
}


function 不检测关键词发送私信流程() {
    logw("开始发送私信");
    ui.toast("开始发送私信")
    let 发送延时 = random(延时最小值, 延时最大值);   //取随机延时
    let 私信话术数组 = 私信话术.split("丨");   //分割话术为数组
    let 随机行数 = random(0, 私信话术数组.length-1);   //取随机行数
    //下面开始写代码.
    click(text("发私信"))    //点击发私信
    sleep(发送延时);
    click(clz("android.widget.EditText"))    //点击输入框
    sleep(发送延时);
    inputText(clz("android.widget.EditText"),私信话术数组[随机行数])
    sleep(发送延时);
    click(desc("发送"))
    sleep(发送延时);
    logw("发送私信完成");
    ui.toast("发送私信完成")
}

function 自动化服务检测(time) {
    for (var i = 0; i < time; i++) {
        if (isServiceOk()) {
            return true;
        }
        var started = startEnv();
        logw("第" + (i + 1) + "次启动服务结果: " + started);
        if (isServiceOk()) {
            return true;
        }
    }
    return isServiceOk();
}   //检测自动化服务

function 发送评论程序() {
    let 发送延时 = random(延时最小值, 延时最大值);   //取随机延时
    let 话术数组 = 私信话术.split("丨");   //分割话术为数组
    let 随机行数 = random(1, 话术数组.length);   //取随机行数
    ui.toast("点击输入框")
    logw("点击输入框");
    click(clz("android.widget.EditText"))   //点击输入框
    sleep(发送延时);
    inputText(clz("android.widget.EditText"),话术数组[随机行数 - 1])   //输入话术
    sleep(发送延时);
    //这里判断用户是否需要添加图片
    ui.toast("判断是否需要加图片")
    logw("判断是否需要加图片");
    if (评论图片){
        //用户需要评论图片
        ui.toast("用户选择需要加图片")
        logw("用户选择需要加图片");
        sleep(发送延时);
        clickEx(desc("插入图片"))
        sleep(发送延时);
        let 相册图片 = getNodeInfo(id("com.ss.android.ugc.aweme:id/root_view").clickable(true),1000)
        sleep(发送延时);
        let 随机选图 = random(1, 相册图片.length);   //取随机延时
        sleep(发送延时);
        相册图片[随机选图-1].click()
        sleep(发送延时);
    }else {
        //不需要加图片
        ui.toast("用户不需要加图片")
        logw("用户不需要加图片");
    }
    click(text("发送"))   //点击发送
    sleep(发送延时);
}

function UID跳转(UID){
    importClass(android.content.Intent);
    importClass(android.net.Uri)
    var intent = new Intent();
    intent.setAction("android.intent.action.VIEW");
    intent.setData(Uri.parse("snssdk1128://user/profile/"+UID))
    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
    try {
        context.startActivity(intent);
    } catch (e) {
        loge(e)
    }
}