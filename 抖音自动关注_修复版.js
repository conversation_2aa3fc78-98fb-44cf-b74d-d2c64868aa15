// 抖音自动关注脚本 - AutoJS6修复版
// 基于原脚本优化，修复AutoJS6兼容性问题

"ui";

ui.layout(
  <vertical padding="16">
    <text text="抖音自动关注工具" textSize="18sp" gravity="center" margin="0 0 16 0"/>
    
    <text text="抖音号列表 (每行一个):" textSize="14sp"/>
    <input id="editUids" hint="请输入抖音号，每行一个" lines="8" gravity="top"/>
    
    <horizontal margin="16 0">
      <text text="关注间隔:" textSize="14sp"/>
      <input id="editInterval" text="5" inputType="number" w="60dp"/>
      <text text="秒" textSize="14sp"/>
    </horizontal>
    
    <horizontal margin="16 0">
      <text text="观看视频:" textSize="14sp"/>
      <input id="editWatchSec" text="10" inputType="number" w="60dp"/>
      <text text="秒" textSize="14sp"/>
    </horizontal>
    
    <horizontal margin="16 0">
      <checkbox id="chkSmart" text="智能模式" checked="true"/>
      <checkbox id="chkBrushVideo" text="刷视频" checked="false"/>
    </horizontal>
    
    <button id="btnStart" text="开始运行" style="Widget.AppCompat.Button.Colored"/>
    <button id="btnStop" text="停止运行" style="Widget.AppCompat.Button"/>
    
    <ScrollView>
      <text id="txtLog" textSize="12sp" maxLines="20"/>
    </ScrollView>
  </vertical>
);

// 全局变量
let running = false;
let paused = false;
let fw = null;
let uidArr = [];
let curIndex = 0;

const PKG = "com.ss.android.ugc.aweme";

// 默认抖音号列表
const DEFAULT_UIDS = [
  "1106768009",
  "dyv9ib5txr5q", 
  "68351324167",
  "clwy1314.",
  "dy7doa8okmrw",
  "1500941473",
  "71557529012"
];

// 初始化
ui.editUids.setText(DEFAULT_UIDS.join("\n"));

// 日志函数
function uiLog(msg) {
  console.log(msg);
  ui.run(() => {
    let current = ui.txtLog.getText().toString();
    let newText = current + "\n" + new Date().toLocaleTimeString() + ": " + msg;
    let lines = newText.split("\n");
    if(lines.length > 20) {
      lines = lines.slice(-20);
      newText = lines.join("\n");
    }
    ui.txtLog.setText(newText);
  });
}

// 解析抖音号列表
function parseUids() {
  let text = ui.editUids.getText().toString().trim();
  if(!text) return [];
  return text.split(/[\r\n]+/).map(s => s.trim()).filter(s => s.length > 0);
}

// 获取配置
function getCfg() {
  return {
    interval: parseInt(ui.editInterval.getText()) || 5,
    watchSec: parseInt(ui.editWatchSec.getText()) || 10,
    smart: ui.chkSmart.isChecked(),
    brushVideo: ui.chkBrushVideo.isChecked()
  };
}

// 启动抖音
function launchDouyin() {
  uiLog("启动抖音...");
  app.launchPackage(PKG);
  sleep(3000);
  uiLog("抖音已启动");
}

// 打开搜索
function openSearch() {
  try {
    let searchBtn = textMatches(/搜索/).findOne(3000) || 
                   descMatches(/搜索/).findOne(1000) ||
                   idMatches(/search/).findOne(1000);
    if(searchBtn) {
      searchBtn.click();
      sleep(1000);
      return className("android.widget.EditText").findOne(2000);
    } else {
      // 兜底：点右上角区域
      click(device.width - 80, 120);
      sleep(1200);
      return className("android.widget.EditText").findOne(2000);
    }
  } catch(e) {
    uiLog("打开搜索失败: " + e);
  }
  return null;
}

// 执行搜索
function doSearch(uid) {
  uiLog("搜索: " + uid);
  let edit = openSearch();
  if(!edit) { 
    uiLog("未找到搜索输入框"); 
    return false; 
  }
  
  try {
    edit.setText("");
    sleep(300);
    edit.setText(uid);
    sleep(600);
    uiLog("输入完成, 执行搜索");
    
    // 多种方式执行搜索
    let searchExecuted = false;
    
    // 方法1：按回车键
    try {
      if(press) {
        press("enter");
        searchExecuted = true;
        uiLog("通过回车键执行搜索");
      }
    } catch(e) {}
    
    // 方法2：查找搜索按钮
    if(!searchExecuted) {
      let searchBtn = textMatches(/搜索|确认|确定/).findOne(1000);
      if(searchBtn) {
        searchBtn.click();
        searchExecuted = true;
        uiLog("通过搜索按钮执行搜索");
      }
    }
    
    // 方法3：点击搜索框右侧
    if(!searchExecuted) {
      click(device.width * 0.9, device.height * 0.15);
      searchExecuted = true;
      uiLog("通过点击搜索框右侧执行搜索");
    }
    
    sleep(3000);
    return true;
  } catch(e) { 
    uiLog("搜索失败:" + e); 
    return false; 
  }
}

// 进入第一个用户主页
function enterFirstUser(uid) {
  uiLog("进入第一个用户");
  
  // 等待页面稳定
  sleep(2000);
  
  // 输出当前页面信息
  try {
    let allTexts = className("android.widget.TextView").find();
    let pageTexts = [];
    for(let i = 0; i < Math.min(allTexts.length, 10); i++) {
      try {
        let text = allTexts[i].text();
        if(text && text.length > 0 && text.length < 20) {
          pageTexts.push(text);
        }
      } catch(e) {}
    }
    uiLog("当前页面主要文本: " + pageTexts.join(", "));
  } catch(e) {
    uiLog("获取页面信息失败: " + e);
  }
  
  // 方法1：通过关注按钮定位用户卡片（主要方法）
  if (text("关注").exists() || text("已关注").exists()) {
    uiLog("找到关注按钮，点击用户卡片进入主页");
    try {
      let followBtn = text("关注").findOne(1000) || text("已关注").findOne(1000);
      if(followBtn) {
        followBtn.parent().click(); // 点击用户卡片
        sleep(2000);
        
        // 检查是否成功进入用户主页
        if(textMatches(/作品|粉丝|获赞/).findOne(2000)) {
          uiLog("通过关注按钮成功进入用户主页");
          return true;
        } else {
          uiLog("点击用户卡片后未进入主页，尝试其他方法");
        }
      }
    } catch(e) {
      uiLog("通过关注按钮点击失败: " + e);
    }
  } else {
    uiLog("未找到关注按钮，尝试其他方法");
  }
  
  // 方法2：通过抖音号定位
  uiLog("尝试通过抖音号定位用户");
  try {
    let candidates = className("android.widget.TextView").textContains(uid).find();
    uiLog("找到 " + candidates.length + " 个包含抖音号的TextView控件");
    
    for(let i = 0; i < candidates.length; i++) {
      let candidate = candidates[i];
      let bounds = candidate.bounds();
      // 排除搜索框区域
      if(bounds.top > device.height * 0.25) {
        uiLog(`找到抖音号控件，位置: (${bounds.left}, ${bounds.top})`);
        
        // 尝试点击抖音号
        candidate.click();
        sleep(2000);
        if(textMatches(/作品|粉丝|获赞/).findOne(1500)) {
          uiLog("通过抖音号成功进入用户主页");
          return true;
        }
        
        // 尝试点击父容器
        try {
          let parent = candidate.parent();
          if(parent) {
            parent.click();
            sleep(2000);
            if(textMatches(/作品|粉丝|获赞/).findOne(1500)) {
              uiLog("通过抖音号父容器成功进入用户主页");
              return true;
            }
          }
        } catch(e) {}
        break;
      }
    }
  } catch(e) { 
    uiLog("通过抖音号定位失败: " + e);
  }
  
  // 方法3：点击头像
  uiLog("尝试点击头像");
  try {
    let imgs = className("android.widget.ImageView").find();
    let topImg = null, topY = 1e9;
    imgs.forEach(img => { 
      try { 
        let b = img.bounds(); 
        if(b && b.top > 100 && b.top < topY) { 
          topY = b.top; 
          topImg = img; 
        } 
      } catch(e) {}
    });
    if(topImg) { 
      topImg.click(); 
      sleep(2000);
      if(textMatches(/作品|粉丝|获赞/).findOne(1500)) {
        uiLog("通过头像成功进入用户主页");
        return true;
      }
    }
  } catch(e) {
    uiLog("点击头像失败: " + e);
  }
  
  uiLog("所有方法都失败，未能进入用户主页");
  return false;
}

// 执行关注
function doFollow() {
  uiLog("尝试关注...");
  try {
    let followBtn = textMatches(/关注|\+ 关注/).findOne(2000);
    if(followBtn) {
      followBtn.click();
      sleep(1000);
      uiLog("关注成功");
      return true;
    } else {
      uiLog("已是关注状态");
      return true;
    }
  } catch(e) {
    uiLog("关注失败: " + e);
    return false;
  }
}

// 观看视频
function watchVideo(cfg) {
  if(!cfg.watchSec || cfg.watchSec <= 0) return;

  uiLog(`观看视频 ${cfg.watchSec}s`);

  // 尝试点击第一个作品进入视频
  click(device.width/3, Math.max(600, device.height*0.55));
  sleep(1500);

  // 检查是否进入了视频播放界面
  if(currentPackage() === PKG){
    click(device.width/2, device.height/2);
    sleep(1500);
  }

  // 观看指定时长
  let watchTime = cfg.watchSec * 1000;
  let startTime = Date.now();

  while(Date.now() - startTime < watchTime && running) {
    sleep(1000);

    // 随机决定是否滑动（30%概率）
    if(random(0, 100) < 30){
      if(random(0, 100) < 50){
        // 上滑到下一个视频
        swipe(device.width/2, device.height*0.8, device.width/2, device.height*0.2, random(300, 600));
        uiLog("上滑到下一个视频");
      } else {
        // 下滑到上一个视频
        swipe(device.width/2, device.height*0.2, device.width/2, device.height*0.8, random(300, 600));
        uiLog("下滑到上一个视频");
      }
      sleep(500);
    }
  }
}

// 刷视频功能
function brushVideos(cfg) {
  if(!cfg.brushVideo) return;

  uiLog("开始刷视频");

  // 刷3个视频
  for(let i = 0; i < 3; i++){
    if(!running) break;

    // 上滑到下一个视频
    swipe(Math.floor(device.width / 2), Math.floor(device.height / 8 * 6),
          Math.floor(device.width / 2), Math.floor(device.height / 8), 800);
    sleep(2500);

    // 随机观看时长
    let watchTime = random(8, 15);
    uiLog(`观看第 ${i + 1}/3 个视频，${watchTime} 秒`);
    sleep(watchTime * 1000);
  }

  uiLog("刷视频完成");
}

// 返回首页
function goHome() {
  uiLog("开始返回首页：准备按返回3次");
  for(let i = 1; i <= 3; i++) {
    back();
    uiLog("已按返回第 " + i + " 次");
    sleep(600);
  }
  uiLog("已按返回3次");
}

// 处理单个用户
function processUser(uid, cfg) {
  uiLog("处理: " + uid);

  // 1. 搜索用户
  if(!doSearch(uid)) {
    uiLog("搜索失败");
    return false;
  }

  // 2. 进入用户主页
  if(!enterFirstUser(uid)) {
    uiLog("未能进入用户主页");
    return false;
  }

  // 3. 执行关注
  if(!doFollow()) {
    uiLog("关注失败");
    return false;
  }

  // 4. 观看视频
  if(cfg.watchSec > 0) {
    watchVideo(cfg);
  }

  // 5. 返回首页
  goHome();

  // 6. 刷视频
  if(cfg.brushVideo) {
    brushVideos(cfg);
  }

  return true;
}

// 创建悬浮窗
function buildFloat(){
  try{ if(fw){ try{fw.close();}catch(e){} fw=null } }catch(e){}

  try {
    // 使用AutoJS6兼容的简单格式
    fw = floaty.window(
      <frame>
        <vertical bg="#77ffffff">
          <text id="taskProgress" text="任务: 0/0" w="auto" h="auto" textSize="12sp"/>
          <text id="currentTask" text="准备中..." w="auto" h="auto" textSize="10sp"/>
          <horizontal>
            <button id="btnPause" text="暂停" w="60" h="35" bg="#77ffffff"/>
            <button id="btnStop" text="停止" w="60" h="35" bg="#77ffffff"/>
          </horizontal>
        </vertical>
      </frame>
    );

    fw.exitOnClose();
    uiLog("悬浮窗创建成功");
  } catch(e) {
    uiLog("悬浮窗创建失败: " + e);
    fw = null;
    return;
  }

  // 如果悬浮窗创建成功，设置事件监听
  if(fw) {
    try {
      // 悬浮窗按钮事件
      fw.btnPause.click(() => {
        paused = !paused;
        fw.btnPause.setText(paused ? "继续" : "暂停");
        uiLog(paused ? "已暂停" : "已继续");
      });

      fw.btnStop.click(() => {
        running = false;
        uiLog("用户停止运行");
      });

      // 更新悬浮窗信息
      function updateFloat() {
        if(fw && running) {
          fw.taskProgress.setText(`任务: ${curIndex}/${uidArr.length}`);
          if(curIndex < uidArr.length) {
            fw.currentTask.setText(uidArr[curIndex]);
          } else {
            fw.currentTask.setText("已完成");
          }
        }
      }

      // 定时更新悬浮窗
      setInterval(updateFloat, 1000);

    } catch(e) {
      uiLog("设置悬浮窗事件失败: " + e);
    }
  }
}

// 主运行函数
function startRun(){
  if(running) return;
  if(!auto.service){
    toast("请先开启无障碍服务");
    auto.waitFor();
  }

  uidArr = parseUids();
  if(uidArr.length === 0){
    toast("请至少输入一个抖音号");
    return;
  }

  let cfg = getCfg();

  running = true;
  paused = false;
  curIndex = 0;

  uiLog(`开始，待处理 ${uidArr.length} 个`);

  // 启动抖音
  launchDouyin();

  // 处理每个用户
  for(let i = 0; i < uidArr.length; i++) {
    if(!running) break;

    curIndex = i;
    let uid = uidArr[i];

    // 等待暂停结束
    while(paused && running) {
      sleep(1000);
    }

    if(!running) break;

    if(processUser(uid, cfg)) {
      uiLog("处理成功: " + uid);
    } else {
      uiLog("处理失败: " + uid);
    }

    // 间隔等待
    if(i < uidArr.length - 1) {
      uiLog(`间隔 ${cfg.interval}s`);
      let waitTime = cfg.interval * 1000;
      let startWait = Date.now();
      while(Date.now() - startWait < waitTime && running && !paused) {
        sleep(100);
      }
    }
  }

  running = false;
  uiLog("全部处理完成");

  // 关闭悬浮窗
  try{ if(fw){ fw.close(); fw = null; } }catch(e){}
}

// UI事件处理
ui.btnStart.click(() => {
  try {
    buildFloat();
    startRun();
  } catch(e) {
    toast('请授权悬浮窗权限');
    try {
      if(floaty.requestPermission) {
        floaty.requestPermission();
      } else {
        toast('请手动开启悬浮窗权限后重试');
      }
    } catch(e2) {
      toast('请手动开启悬浮窗权限后重试');
    }
  }
});

ui.btnStop.click(() => {
  running = false;
  try{ if(fw){ fw.close(); fw = null; } }catch(e){}
  uiLog("已停止运行");
});

// 应用退出时清理
events.on("exit", () => {
  running = false;
  try{ if(fw){ fw.close(); fw = null; } }catch(e){}
});
